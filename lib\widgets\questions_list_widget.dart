import 'package:flutter/material.dart';
import '../models/question_models.dart';

class QuestionsListWidget extends StatelessWidget {
  final List<Question> questions;
  final Function(int) onRemoveQuestion;

  const QuestionsListWidget({
    super.key,
    required this.questions,
    required this.onRemoveQuestion,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الأسئلة المضافة (${questions.length})',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: questions.length,
          itemBuilder: (context, index) {
            final question = questions[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 12),
              child: ExpansionTile(
                title: Text(
                  'السؤال ${index + 1}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Text(
                  _getQuestionPreview(question.question),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                trailing: IconButton(
                  onPressed: () => _showDeleteConfirmation(context, index),
                  icon: const Icon(Icons.delete, color: Colors.red),
                  tooltip: 'حذف السؤال',
                ),
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Question Content
                        _buildContentSection(
                          'السؤال:',
                          question.question,
                          Icons.help_outline,
                        ),

                        const SizedBox(height: 16),

                        // Options
                        const Text(
                          'الخيارات:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        ...question.options.asMap().entries.map((entry) {
                          final index = entry.key;
                          final option = entry.value;
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Row(
                              children: [
                                Icon(
                                  option.isCorrect
                                      ? Icons.check_circle
                                      : Icons.radio_button_unchecked,
                                  color: option.isCorrect
                                      ? Colors.green
                                      : Colors.grey,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    '${String.fromCharCode(65 + index)}. ${_getContentText(option.answer, option.type)}',
                                    style: TextStyle(
                                      fontWeight: option.isCorrect
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                      color: option.isCorrect
                                          ? Colors.green[700]
                                          : null,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }),

                        // Explanation
                        if (question.explanation != null) ...[
                          const SizedBox(height: 16),
                          _buildContentSection(
                            'الشرح:',
                            question.explanation!,
                            Icons.lightbulb_outline,
                          ),
                        ],

                        // Explanation Image
                        if (question.explanationImageUrl != null) ...[
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              const Icon(Icons.image, size: 16),
                              const SizedBox(width: 8),
                              const Text(
                                'صورة الشرح:',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  question.explanationImageUrl!,
                                  style: const TextStyle(
                                    color: Colors.blue,
                                    decoration: TextDecoration.underline,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildContentSection(String title, Content content, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16),
            const SizedBox(width: 8),
            Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: _getTypeColor(content.type),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _getTypeLabel(content.type),
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Text(
            _getContentText(content.content, content.type),
            style: TextStyle(
              fontFamily: content.type == ContentType.latex
                  ? 'monospace'
                  : null,
            ),
          ),
        ),
      ],
    );
  }

  String _getQuestionPreview(Content question) {
    return _getContentText(question.content, question.type);
  }

  String _getContentText(String content, ContentType type) {
    switch (type) {
      case ContentType.text:
        return content;
      case ContentType.latex:
        return content;
      case ContentType.image:
        return '[صورة] $content';
    }
  }

  String _getTypeLabel(ContentType type) {
    switch (type) {
      case ContentType.text:
        return 'نص';
      case ContentType.latex:
        return 'لاتكس';
      case ContentType.image:
        return 'صورة';
    }
  }

  Color _getTypeColor(ContentType type) {
    switch (type) {
      case ContentType.text:
        return Colors.blue;
      case ContentType.latex:
        return Colors.purple;
      case ContentType.image:
        return Colors.green;
    }
  }

  void _showDeleteConfirmation(BuildContext context, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف السؤال ${index + 1}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onRemoveQuestion(index);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
