{"dart.flutterSdkPath": null, "dart.lineLength": 80, "dart.insertArgumentPlaceholders": false, "dart.showTodos": true, "dart.closingLabels": true, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80], "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "first", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off", "files.associations": {"*.dart": "dart"}, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/build": true, "**/.dart_tool": true, "**/.pub": true}, "search.exclude": {"**/build": true, "**/.dart_tool": true, "**/.pub": true}}