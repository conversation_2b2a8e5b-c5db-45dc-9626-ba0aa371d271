import 'package:flutter/material.dart';
import 'app_config.dart';

/// Theme configuration for the Question Creator app
class AppTheme {
  // Private constructor to prevent instantiation
  AppTheme._();

  // Color scheme
  static const Color primaryColor = Color(AppConfig.primaryColorValue);
  static const Color successColor = Color(AppConfig.successColorValue);
  static const Color errorColor = Color(AppConfig.errorColorValue);
  static const Color warningColor = Color(AppConfig.warningColorValue);

  // Light theme
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.light,
      ),

      // App Bar Theme
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: 2,
        shadowColor: Colors.black26,
      ),

      // Card Theme
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        ),
        margin: const EdgeInsets.symmetric(vertical: 4),
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: AppConfig.buttonPadding,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          ),
          elevation: 2,
        ),
      ),

      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: AppConfig.buttonPadding,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          ),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          borderSide: BorderSide(color: Colors.grey.shade400),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          borderSide: const BorderSide(color: errorColor, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConfig.defaultPadding,
          vertical: AppConfig.buttonPadding,
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
      ),

      // Dropdown Theme
      dropdownMenuTheme: DropdownMenuThemeData(
        inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: AppConfig.defaultPadding,
            vertical: AppConfig.buttonPadding,
          ),
        ),
      ),

      // Dialog Theme
      dialogTheme: DialogThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius * 2),
        ),
        elevation: 8,
      ),

      // Snack Bar Theme
      snackBarTheme: SnackBarThemeData(
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        ),
        elevation: 4,
      ),

      // Divider Theme
      dividerTheme: const DividerThemeData(thickness: 1, space: 32),

      // Icon Theme
      iconTheme: const IconThemeData(size: 24),

      // Text Theme (Arabic-friendly)
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          height: 1.2,
        ),
        headlineMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          height: 1.2,
        ),
        headlineSmall: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          height: 1.2,
        ),
        titleLarge: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          height: 1.3,
        ),
        titleMedium: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          height: 1.3,
        ),
        titleSmall: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          height: 1.3,
        ),
        bodyLarge: TextStyle(fontSize: 16, height: 1.5),
        bodyMedium: TextStyle(fontSize: 14, height: 1.5),
        bodySmall: TextStyle(fontSize: 12, height: 1.4),
        labelLarge: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          height: 1.4,
        ),
        labelMedium: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          height: 1.4,
        ),
        labelSmall: TextStyle(
          fontSize: 11,
          fontWeight: FontWeight.w500,
          height: 1.4,
        ),
      ),
    );
  }

  // Dark theme (for future use)
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.dark,
      ),
      // Add dark theme specific configurations here
    );
  }

  // Custom colors for specific use cases
  static const Color correctAnswerColor = successColor;
  static const Color incorrectAnswerColor = Colors.grey;
  static const Color latexBackgroundColor = Color(0xFFF5F5F5);
  static const Color imageBackgroundColor = Color(0xFFE8F5E8);
  static const Color textBackgroundColor = Color(0xFFE3F2FD);

  // Content type colors
  static Color getContentTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'text':
        return primaryColor;
      case 'latex':
        return Colors.purple;
      case 'image':
        return successColor;
      default:
        return Colors.grey;
    }
  }

  // Helper methods for consistent styling
  static BoxDecoration getCardDecoration({Color? color}) {
    return BoxDecoration(
      color: color ?? Colors.white,
      borderRadius: BorderRadius.circular(AppConfig.borderRadius),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.1),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  static BoxDecoration getContentTypeDecoration(String type) {
    return BoxDecoration(
      color: getContentTypeColor(type).withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(AppConfig.borderRadius / 2),
      border: Border.all(
        color: getContentTypeColor(type).withValues(alpha: 0.3),
        width: 1,
      ),
    );
  }

  static TextStyle getContentTypeTextStyle(String type) {
    return TextStyle(
      color: getContentTypeColor(type),
      fontWeight: FontWeight.bold,
      fontSize: 12,
    );
  }

  // Button styles for different actions
  static ButtonStyle get addButtonStyle {
    return ElevatedButton.styleFrom(
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
    );
  }

  static ButtonStyle get deleteButtonStyle {
    return ElevatedButton.styleFrom(
      backgroundColor: errorColor,
      foregroundColor: Colors.white,
    );
  }

  static ButtonStyle get exportButtonStyle {
    return ElevatedButton.styleFrom(
      backgroundColor: successColor,
      foregroundColor: Colors.white,
    );
  }

  static ButtonStyle get warningButtonStyle {
    return ElevatedButton.styleFrom(
      backgroundColor: warningColor,
      foregroundColor: Colors.white,
    );
  }
}
