// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:creat_json/main.dart';

void main() {
  testWidgets('Question Creator App loads correctly', (
    WidgetTester tester,
  ) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const QuestionCreatorApp());

    // Verify that the app title is displayed
    expect(find.text('منشئ الأسئلة للمدرسين'), findsOneWidget);

    // Verify that the question form is displayed
    expect(find.text('السؤال *'), findsOneWidget);
    expect(find.text('خيارات الإجابة *'), findsOneWidget);
    expect(find.text('الشرح (اختياري)'), findsOneWidget);

    // Verify that the add question button is displayed
    expect(find.text('إضافة السؤال'), findsOneWidget);
  });

  testWidgets('Question form validation works', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const QuestionCreatorApp());

    // Find the add question button
    final addButton = find.text('إضافة السؤال');

    // Scroll to make the button visible if needed
    if (addButton.evaluate().isNotEmpty) {
      await tester.ensureVisible(addButton.first);
    }

    // Try to add a question without filling the form
    await tester.tap(addButton, warnIfMissed: false);
    await tester.pumpAndSettle();

    // Verify that validation errors are shown
    expect(find.text('يجب إدخال نص السؤال'), findsOneWidget);
  });
}
