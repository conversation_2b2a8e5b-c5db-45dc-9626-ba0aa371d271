import 'package:flutter/material.dart';
import '../models/question_models.dart';
import '../utils/validators.dart';

class QuestionFormWidget extends StatelessWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController questionController;
  final TextEditingController explanationController;
  final TextEditingController explanationImageController;
  final ContentType questionType;
  final ContentType explanationType;
  final List<TextEditingController> optionControllers;
  final List<ContentType> optionTypes;
  final List<bool> correctAnswers;
  final Function(ContentType) onQuestionTypeChanged;
  final Function(ContentType) onExplanationTypeChanged;
  final Function(int, ContentType) onOptionTypeChanged;
  final Function(int, bool) onCorrectAnswerChanged;
  final VoidCallback onAddOption;
  final Function(int) onRemoveOption;
  final VoidCallback onAddQuestion;

  const QuestionFormWidget({
    super.key,
    required this.formKey,
    required this.questionController,
    required this.explanationController,
    required this.explanationImageController,
    required this.questionType,
    required this.explanationType,
    required this.optionControllers,
    required this.optionTypes,
    required this.correctAnswers,
    required this.onQuestionTypeChanged,
    required this.onExplanationTypeChanged,
    required this.onOptionTypeChanged,
    required this.onCorrectAnswerChanged,
    required this.onAddOption,
    required this.onRemoveOption,
    required this.onAddQuestion,
  });

  @override
  Widget build(BuildContext context) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Question Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'السؤال *',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: TextFormField(
                          controller: questionController,
                          decoration: InputDecoration(
                            hintText: _getHintText(questionType, 'السؤال'),
                            border: const OutlineInputBorder(),
                          ),
                          maxLines: questionType == ContentType.latex ? 3 : 2,
                          validator: (value) {
                            if (!Validators.isNotEmpty(value)) {
                              return 'يجب إدخال نص السؤال';
                            }
                            if (questionType == ContentType.latex && 
                                !Validators.isValidLatex(value!)) {
                              return 'صيغة LaTeX غير صحيحة';
                            }
                            if (questionType == ContentType.image && 
                                !Validators.isValidGoogleDriveUrl(value!)) {
                              return 'رابط Google Drive غير صحيح';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        flex: 1,
                        child: DropdownButtonFormField<ContentType>(
                          value: questionType,
                          decoration: const InputDecoration(
                            labelText: 'النوع',
                            border: OutlineInputBorder(),
                          ),
                          items: ContentType.values.map((type) {
                            return DropdownMenuItem(
                              value: type,
                              child: Text(_getTypeLabel(type)),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              onQuestionTypeChanged(value);
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Options Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'خيارات الإجابة *',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  ...List.generate(optionControllers.length, (index) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Radio<int>(
                            value: index,
                            groupValue: correctAnswers.indexWhere((element) => element),
                            onChanged: (value) {
                              if (value != null) {
                                // Reset all correct answers
                                for (int i = 0; i < correctAnswers.length; i++) {
                                  onCorrectAnswerChanged(i, i == value);
                                }
                              }
                            },
                          ),
                          Expanded(
                            flex: 3,
                            child: TextFormField(
                              controller: optionControllers[index],
                              decoration: InputDecoration(
                                hintText: _getHintText(optionTypes[index], 'الخيار ${index + 1}'),
                                border: const OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (!Validators.isNotEmpty(value)) {
                                  return 'يجب إدخال نص الخيار';
                                }
                                if (optionTypes[index] == ContentType.latex && 
                                    !Validators.isValidLatex(value!)) {
                                  return 'صيغة LaTeX غير صحيحة';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            flex: 1,
                            child: DropdownButtonFormField<ContentType>(
                              value: optionTypes[index],
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                              ),
                              items: [ContentType.text, ContentType.latex].map((type) {
                                return DropdownMenuItem(
                                  value: type,
                                  child: Text(_getTypeLabel(type)),
                                );
                              }).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  onOptionTypeChanged(index, value);
                                }
                              },
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (optionControllers.length > 2)
                            IconButton(
                              onPressed: () => onRemoveOption(index),
                              icon: const Icon(Icons.close, color: Colors.red),
                              tooltip: 'حذف الخيار',
                            ),
                        ],
                      ),
                    );
                  }),
                  if (optionControllers.length < 10)
                    TextButton.icon(
                      onPressed: onAddOption,
                      icon: const Icon(Icons.add),
                      label: const Text('إضافة خيار'),
                    ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Explanation Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الشرح (اختياري)',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: TextFormField(
                          controller: explanationController,
                          decoration: InputDecoration(
                            hintText: _getHintText(explanationType, 'شرح الإجابة'),
                            border: const OutlineInputBorder(),
                          ),
                          maxLines: explanationType == ContentType.latex ? 3 : 2,
                          validator: (value) {
                            if (value != null && value.trim().isNotEmpty) {
                              if (explanationType == ContentType.latex && 
                                  !Validators.isValidLatex(value)) {
                                return 'صيغة LaTeX غير صحيحة';
                              }
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        flex: 1,
                        child: DropdownButtonFormField<ContentType>(
                          value: explanationType,
                          decoration: const InputDecoration(
                            labelText: 'النوع',
                            border: OutlineInputBorder(),
                          ),
                          items: [ContentType.text, ContentType.latex].map((type) {
                            return DropdownMenuItem(
                              value: type,
                              child: Text(_getTypeLabel(type)),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              onExplanationTypeChanged(value);
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: explanationImageController,
                    decoration: const InputDecoration(
                      hintText: 'رابط صورة الشرح من Google Drive (اختياري)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.image),
                    ),
                    validator: (value) {
                      if (value != null && value.trim().isNotEmpty) {
                        if (!Validators.isValidGoogleDriveUrl(value)) {
                          return 'رابط Google Drive غير صحيح';
                        }
                      }
                      return null;
                    },
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Add Question Button
          ElevatedButton.icon(
            onPressed: onAddQuestion,
            icon: const Icon(Icons.add),
            label: const Text('إضافة السؤال'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ],
      ),
    );
  }

  String _getTypeLabel(ContentType type) {
    switch (type) {
      case ContentType.text:
        return 'نص';
      case ContentType.latex:
        return 'لاتكس';
      case ContentType.image:
        return 'صورة';
    }
  }

  String _getHintText(ContentType type, String field) {
    switch (type) {
      case ContentType.text:
        return 'أدخل $field';
      case ContentType.latex:
        return 'أدخل كود LaTeX للـ$field';
      case ContentType.image:
        return 'أدخل رابط Google Drive للصورة';
    }
  }
}
