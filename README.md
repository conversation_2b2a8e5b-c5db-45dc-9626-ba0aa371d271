# منشئ الأسئلة للمدرسين - Question Creator for Teachers

تطبيق Flutter يسمح للمدرسين بإنشاء بنوك أسئلة بصيغة JSON منظمة تدعم النصوص ومعادلات LaTeX والصور من Google Drive.

## المميزات الرئيسية

### 📝 إنشاء الأسئلة
- دعم ثلاثة أنواع من المحتوى:
  - **نص عادي**: للأسئلة النصية التقليدية
  - **معادلات LaTeX**: للمعادلات الرياضية والعلمية
  - **صور Google Drive**: لإدراج الصور والرسوم البيانية

### ✅ خيارات الإجابة المرنة
- إضافة من 2 إلى 10 خيارات للسؤال الواحد
- دعم النص العادي و LaTeX في الخيارات
- تحديد الإجابة الصحيحة بسهولة
- إمكانية حذف وإضافة الخيارات ديناميكياً

### 💡 الشرح والتوضيح
- إضافة شرح اختياري للإجابة (نص أو LaTeX)
- إمكانية إرفاق صورة توضيحية من Google Drive
- عرض مفصل للأسئلة المضافة

### 📤 التصدير والمشاركة
- تصدير الأسئلة بصيغة JSON منظمة
- مشاركة الملفات مباشرة
- هيكل JSON متوافق مع تطبيق الأدمن

## هيكل ملف JSON الناتج

```json
{
  "questions": [
    {
      "question": {
        "content": "ما هي عاصمة فرنسا؟",
        "type": "text"
      },
      "options": [
        {
          "answer": "لندن",
          "type": "text",
          "is_correct": false
        },
        {
          "answer": "باريس",
          "type": "text",
          "is_correct": true
        }
      ],
      "explanation": {
        "content": "باريس هي العاصمة منذ القرن الخامس.",
        "type": "text"
      },
      "explanation_image_url": null
    }
  ]
}
```

## التقنيات المستخدمة

- **Flutter**: إطار العمل الأساسي
- **file_picker**: لاختيار مكان حفظ الملفات
- **share_plus**: لمشاركة الملفات
- **url_launcher**: للتحقق من روابط Google Drive
- **path_provider**: للوصول إلى مجلدات النظام

## كيفية الاستخدام

### 1. إنشاء سؤال جديد
1. أدخل نص السؤال واختر نوعه (نص/لاتكس/صورة)
2. أضف خيارات الإجابة (2-10 خيارات)
3. حدد الإجابة الصحيحة
4. أضف شرحاً اختيارياً
5. اضغط "إضافة السؤال"

### 2. إدارة الأسئلة
- عرض جميع الأسئلة المضافة
- حذف أي سؤال غير مرغوب فيه
- معاينة تفاصيل كل سؤال

### 3. تصدير الأسئلة
- اضغط زر "تصدير الأسئلة"
- اختر مكان الحفظ أو المشاركة المباشرة
- احصل على ملف JSON جاهز للاستيراد

## التشغيل والتطوير

### متطلبات النظام
- Flutter SDK 3.8.1 أو أحدث
- Dart SDK
- محرر نصوص (VS Code, Android Studio)

### تشغيل التطبيق
```bash
# تحديث التبعيات
flutter pub get

# تشغيل التطبيق
flutter run

# تشغيل الاختبارات
flutter test

# بناء التطبيق للإنتاج
flutter build windows  # أو ios/android حسب المنصة
```

## هيكل المشروع

```
lib/
├── main.dart                    # نقطة البداية
├── models/
│   └── question_models.dart     # نماذج البيانات
├── screens/
│   └── question_creator_screen.dart  # الشاشة الرئيسية
├── widgets/
│   ├── question_form_widget.dart     # نموذج إنشاء السؤال
│   └── questions_list_widget.dart    # قائمة الأسئلة
├── services/
│   └── export_service.dart           # خدمة التصدير
└── utils/
    └── validators.dart               # أدوات التحقق
```

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة الاختبارات المناسبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.
