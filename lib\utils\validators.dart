import 'package:url_launcher/url_launcher.dart';

/// Utility class for validation functions
class Validators {
  /// Validates if a string is not empty after trimming
  static bool isNotEmpty(String? value) {
    return value != null && value.trim().isNotEmpty;
  }

  /// Validates if a Google Drive URL is in the correct format
  static bool isValidGoogleDriveUrl(String url) {
    if (url.trim().isEmpty) return false;
    
    // Check if it's a valid URL
    final uri = Uri.tryParse(url);
    if (uri == null) return false;
    
    // Check if it's a Google Drive URL
    return url.contains('drive.google.com') || 
           url.contains('docs.google.com') ||
           url.contains('googleapis.com');
  }

  /// Validates if a URL is accessible
  static Future<bool> isUrlAccessible(String url) async {
    try {
      final uri = Uri.parse(url);
      return await canLaunchUrl(uri);
    } catch (e) {
      return false;
    }
  }

  /// Validates LaTeX syntax (basic validation)
  static bool isValidLatex(String latex) {
    if (latex.trim().isEmpty) return false;
    
    // Basic LaTeX validation - check for balanced braces
    int braceCount = 0;
    for (int i = 0; i < latex.length; i++) {
      if (latex[i] == '{') {
        braceCount++;
      } else if (latex[i] == '}') {
        braceCount--;
        if (braceCount < 0) return false;
      }
    }
    
    return braceCount == 0;
  }

  /// Validates if at least one option is marked as correct
  static bool hasCorrectAnswer(List<bool> correctAnswers) {
    return correctAnswers.any((isCorrect) => isCorrect);
  }

  /// Validates if there are at least minimum number of options
  static bool hasMinimumOptions(List<dynamic> options, {int minimum = 2}) {
    return options.length >= minimum;
  }
}
