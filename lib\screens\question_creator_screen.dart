import 'package:flutter/material.dart';
import '../models/question_models.dart';
import '../utils/validators.dart';
import '../services/export_service.dart';
import '../widgets/question_form_widget.dart';
import '../widgets/questions_list_widget.dart';

class QuestionCreatorScreen extends StatefulWidget {
  const QuestionCreatorScreen({super.key});

  @override
  State<QuestionCreatorScreen> createState() => _QuestionCreatorScreenState();
}

class _QuestionCreatorScreenState extends State<QuestionCreatorScreen> {
  final List<Question> _questions = [];
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  
  // Controllers for form fields
  final TextEditingController _questionController = TextEditingController();
  final TextEditingController _explanationController = TextEditingController();
  final TextEditingController _explanationImageController = TextEditingController();
  
  // Form state
  ContentType _questionType = ContentType.text;
  ContentType _explanationType = ContentType.text;
  List<TextEditingController> _optionControllers = [];
  List<ContentType> _optionTypes = [];
  List<bool> _correctAnswers = [];
  
  @override
  void initState() {
    super.initState();
    _initializeOptions();
  }

  void _initializeOptions() {
    // Initialize with 4 default options
    _optionControllers = List.generate(4, (index) => TextEditingController());
    _optionTypes = List.generate(4, (index) => ContentType.text);
    _correctAnswers = List.generate(4, (index) => false);
  }

  @override
  void dispose() {
    _questionController.dispose();
    _explanationController.dispose();
    _explanationImageController.dispose();
    for (var controller in _optionControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _addOption() {
    if (_optionControllers.length < 10) {
      setState(() {
        _optionControllers.add(TextEditingController());
        _optionTypes.add(ContentType.text);
        _correctAnswers.add(false);
      });
    }
  }

  void _removeOption(int index) {
    if (_optionControllers.length > 2) {
      setState(() {
        _optionControllers[index].dispose();
        _optionControllers.removeAt(index);
        _optionTypes.removeAt(index);
        _correctAnswers.removeAt(index);
      });
    }
  }

  void _addQuestion() {
    if (_formKey.currentState!.validate()) {
      // Validate that at least one answer is correct
      if (!Validators.hasCorrectAnswer(_correctAnswers)) {
        _showErrorDialog('يجب تحديد إجابة صحيحة واحدة على الأقل');
        return;
      }

      // Create question options
      final options = <QuestionOption>[];
      for (int i = 0; i < _optionControllers.length; i++) {
        if (_optionControllers[i].text.trim().isNotEmpty) {
          options.add(QuestionOption(
            answer: _optionControllers[i].text.trim(),
            type: _optionTypes[i],
            isCorrect: _correctAnswers[i],
          ));
        }
      }

      // Create question
      final question = Question(
        question: Content(
          content: _questionController.text.trim(),
          type: _questionType,
        ),
        options: options,
        explanation: _explanationController.text.trim().isNotEmpty
            ? Content(
                content: _explanationController.text.trim(),
                type: _explanationType,
              )
            : null,
        explanationImageUrl: _explanationImageController.text.trim().isNotEmpty
            ? _explanationImageController.text.trim()
            : null,
      );

      setState(() {
        _questions.add(question);
      });

      _clearForm();
      _showSuccessDialog('تم إضافة السؤال بنجاح!');
    }
  }

  void _clearForm() {
    _questionController.clear();
    _explanationController.clear();
    _explanationImageController.clear();
    
    for (var controller in _optionControllers) {
      controller.clear();
    }
    
    setState(() {
      _questionType = ContentType.text;
      _explanationType = ContentType.text;
      _optionTypes = List.generate(_optionControllers.length, (index) => ContentType.text);
      _correctAnswers = List.generate(_optionControllers.length, (index) => false);
    });
  }

  void _exportQuestions() async {
    if (_questions.isEmpty) {
      _showErrorDialog('لا توجد أسئلة للتصدير');
      return;
    }

    final success = await ExportService.exportQuestions(_questions);
    
    if (success) {
      _showSuccessDialog('تم تصدير الأسئلة بنجاح!');
    } else {
      _showErrorDialog('فشل في تصدير الأسئلة');
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('نجح'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('منشئ الأسئلة للمدرسين'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_questions.isNotEmpty)
            IconButton(
              onPressed: _exportQuestions,
              icon: const Icon(Icons.download),
              tooltip: 'تصدير الأسئلة',
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Question Form
            QuestionFormWidget(
              formKey: _formKey,
              questionController: _questionController,
              explanationController: _explanationController,
              explanationImageController: _explanationImageController,
              questionType: _questionType,
              explanationType: _explanationType,
              optionControllers: _optionControllers,
              optionTypes: _optionTypes,
              correctAnswers: _correctAnswers,
              onQuestionTypeChanged: (type) => setState(() => _questionType = type),
              onExplanationTypeChanged: (type) => setState(() => _explanationType = type),
              onOptionTypeChanged: (index, type) => setState(() => _optionTypes[index] = type),
              onCorrectAnswerChanged: (index, value) => setState(() => _correctAnswers[index] = value),
              onAddOption: _addOption,
              onRemoveOption: _removeOption,
              onAddQuestion: _addQuestion,
            ),
            
            const SizedBox(height: 24),
            
            // Questions List
            if (_questions.isNotEmpty) ...[
              const Divider(),
              const SizedBox(height: 16),
              QuestionsListWidget(
                questions: _questions,
                onRemoveQuestion: (index) => setState(() => _questions.removeAt(index)),
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: _exportQuestions,
                icon: const Icon(Icons.download),
                label: Text('تصدير ${_questions.length} سؤال'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
