/// Configuration constants for the Question Creator app
library;

class AppConfig {
  // App Information
  static const String appName = 'منشئ الأسئلة للمدرسين';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق لإنشاء بنوك الأسئلة بصيغة JSON';

  // Question Limits
  static const int minOptionsPerQuestion = 2;
  static const int maxOptionsPerQuestion = 10;
  static const int defaultOptionsCount = 4;

  // File Settings
  static const String defaultFileName = 'questions';
  static const String fileExtension = 'json';
  static const String exportMimeType = 'application/json';

  // Google Drive URL Patterns
  static const List<String> validGoogleDrivePatterns = [
    'drive.google.com',
    'docs.google.com',
    'googleapis.com',
  ];

  // LaTeX Validation
  static const List<String> commonLatexCommands = [
    '\\frac',
    '\\sqrt',
    '\\sum',
    '\\int',
    '\\alpha',
    '\\beta',
    '\\gamma',
    '\\delta',
    '\\pi',
    '\\theta',
  ];

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double cardPadding = 16.0;
  static const double buttonPadding = 12.0;
  static const double borderRadius = 8.0;

  // Colors (Material 3 compatible)
  static const int primaryColorValue = 0xFF2196F3; // Blue
  static const int successColorValue = 0xFF4CAF50; // Green
  static const int errorColorValue = 0xFFF44336; // Red
  static const int warningColorValue = 0xFFFF9800; // Orange

  // Text Limits
  static const int maxQuestionLength = 1000;
  static const int maxOptionLength = 200;
  static const int maxExplanationLength = 2000;

  // Export Settings
  static const String jsonIndentation = '  '; // 2 spaces
  static const bool prettyPrintJson = true;

  // Validation Messages (Arabic)
  static const Map<String, String> validationMessages = {
    'emptyQuestion': 'يجب إدخال نص السؤال',
    'emptyOption': 'يجب إدخال نص الخيار',
    'invalidLatex': 'صيغة LaTeX غير صحيحة',
    'invalidGoogleDriveUrl': 'رابط Google Drive غير صحيح',
    'noCorrectAnswer': 'يجب تحديد إجابة صحيحة واحدة على الأقل',
    'minimumOptions': 'يجب إضافة خيارين على الأقل',
    'maximumOptions': 'لا يمكن إضافة أكثر من 10 خيارات',
    'questionTooLong': 'نص السؤال طويل جداً',
    'optionTooLong': 'نص الخيار طويل جداً',
    'explanationTooLong': 'نص الشرح طويل جداً',
  };

  // Success Messages (Arabic)
  static const Map<String, String> successMessages = {
    'questionAdded': 'تم إضافة السؤال بنجاح!',
    'questionsExported': 'تم تصدير الأسئلة بنجاح!',
    'questionDeleted': 'تم حذف السؤال بنجاح!',
  };

  // Error Messages (Arabic)
  static const Map<String, String> errorMessages = {
    'exportFailed': 'فشل في تصدير الأسئلة',
    'noQuestionsToExport': 'لا توجد أسئلة للتصدير',
    'invalidJsonStructure': 'هيكل JSON غير صحيح',
    'fileAccessError': 'خطأ في الوصول للملف',
    'networkError': 'خطأ في الاتصال بالشبكة',
  };

  // Dialog Titles (Arabic)
  static const Map<String, String> dialogTitles = {
    'error': 'خطأ',
    'success': 'نجح',
    'warning': 'تحذير',
    'confirmation': 'تأكيد',
    'deleteQuestion': 'تأكيد الحذف',
  };

  // Button Labels (Arabic)
  static const Map<String, String> buttonLabels = {
    'add': 'إضافة',
    'delete': 'حذف',
    'edit': 'تعديل',
    'save': 'حفظ',
    'cancel': 'إلغاء',
    'ok': 'موافق',
    'export': 'تصدير',
    'share': 'مشاركة',
    'addQuestion': 'إضافة السؤال',
    'addOption': 'إضافة خيار',
    'exportQuestions': 'تصدير الأسئلة',
  };

  // Content Type Labels (Arabic)
  static const Map<String, String> contentTypeLabels = {
    'text': 'نص',
    'latex': 'لاتكس',
    'image': 'صورة',
  };

  // Hint Texts (Arabic)
  static const Map<String, String> hintTexts = {
    'questionText': 'أدخل نص السؤال',
    'questionLatex': 'أدخل كود LaTeX للسؤال',
    'questionImage': 'أدخل رابط Google Drive للصورة',
    'optionText': 'أدخل نص الخيار',
    'optionLatex': 'أدخل كود LaTeX للخيار',
    'explanationText': 'أدخل شرح الإجابة',
    'explanationLatex': 'أدخل كود LaTeX للشرح',
    'explanationImage': 'رابط صورة الشرح من Google Drive (اختياري)',
  };

  // Helper Methods
  static String getValidationMessage(String key) {
    return validationMessages[key] ?? 'خطأ غير معروف';
  }

  static String getSuccessMessage(String key) {
    return successMessages[key] ?? 'تمت العملية بنجاح';
  }

  static String getErrorMessage(String key) {
    return errorMessages[key] ?? 'حدث خطأ غير متوقع';
  }

  static String getDialogTitle(String key) {
    return dialogTitles[key] ?? 'رسالة';
  }

  static String getButtonLabel(String key) {
    return buttonLabels[key] ?? key;
  }

  static String getContentTypeLabel(String key) {
    return contentTypeLabels[key] ?? key;
  }

  static String getHintText(String key) {
    return hintTexts[key] ?? '';
  }
}
