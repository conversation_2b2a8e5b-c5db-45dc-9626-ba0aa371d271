import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import '../models/question_models.dart';

/// Service for exporting questions to JSON format
class ExportService {
  /// Exports questions to a JSON file and allows user to save or share
  static Future<bool> exportQuestions(
    List<Question> questions, {
    String fileName = 'questions',
  }) async {
    try {
      // Create question bank
      final questionBank = QuestionBank(questions: questions);

      // Convert to JSON
      final jsonString = const JsonEncoder.withIndent(
        '  ',
      ).convert(questionBank.toJson());

      // Get temporary directory
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/$fileName.json');

      // Write JSON to file
      await file.writeAsString(jsonString, encoding: utf8);

      // Let user choose to save or share
      final result = await _showExportOptions(file);

      return result;
    } catch (e) {
      debugPrint('Error exporting questions: $e');
      return false;
    }
  }

  /// Shows options to save or share the exported file
  static Future<bool> _showExportOptions(File file) async {
    try {
      // For now, we'll use the share functionality
      // In a real app, you might want to show a dialog with options

      final result = await Share.shareXFiles(
        [XFile(file.path)],
        text: 'بنك الأسئلة - Questions Bank',
        subject: 'Questions Export',
      );

      return result.status == ShareResultStatus.success;
    } catch (e) {
      debugPrint('Error sharing file: $e');
      return false;
    }
  }

  /// Allows user to save file to a custom location
  static Future<bool> saveToCustomLocation(File sourceFile) async {
    try {
      String? outputFile = await FilePicker.platform.saveFile(
        dialogTitle: 'حفظ ملف الأسئلة',
        fileName: 'questions.json',
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (outputFile != null) {
        final targetFile = File(outputFile);
        await sourceFile.copy(targetFile.path);
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Error saving to custom location: $e');
      return false;
    }
  }

  /// Validates the exported JSON structure
  static bool validateExportedJson(String jsonString) {
    try {
      final decoded = jsonDecode(jsonString);

      // Check if it has the required structure
      if (decoded is! Map<String, dynamic>) return false;
      if (!decoded.containsKey('questions')) return false;
      if (decoded['questions'] is! List) return false;

      final questions = decoded['questions'] as List;

      // Validate each question structure
      for (final question in questions) {
        if (question is! Map<String, dynamic>) return false;
        if (!question.containsKey('question')) return false;
        if (!question.containsKey('options')) return false;

        final options = question['options'];
        if (options is! List || options.isEmpty) return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Creates a preview of the JSON structure
  static String createJsonPreview(List<Question> questions) {
    if (questions.isEmpty) {
      return '{\n  "questions": []\n}';
    }

    final questionBank = QuestionBank(questions: questions);
    return const JsonEncoder.withIndent('  ').convert(questionBank.toJson());
  }
}
