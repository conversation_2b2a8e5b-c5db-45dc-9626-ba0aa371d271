/// Data models for the question creator app
library;

enum ContentType { text, latex, image }

/// Represents the content of a question, option, or explanation
class Content {
  final String content;
  final ContentType type;

  Content({required this.content, required this.type});

  Map<String, dynamic> toJson() {
    return {'content': content, 'type': type.name};
  }

  factory Content.fromJson(Map<String, dynamic> json) {
    return Content(
      content: json['content'] as String,
      type: ContentType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ContentType.text,
      ),
    );
  }
}

/// Represents an answer option for a question
class QuestionOption {
  final String answer;
  final ContentType type;
  final bool isCorrect;

  QuestionOption({
    required this.answer,
    required this.type,
    required this.isCorrect,
  });

  Map<String, dynamic> toJson() {
    return {'answer': answer, 'type': type.name, 'is_correct': isCorrect};
  }

  factory QuestionOption.fromJson(Map<String, dynamic> json) {
    return QuestionOption(
      answer: json['answer'] as String,
      type: ContentType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ContentType.text,
      ),
      isCorrect: json['is_correct'] as bool,
    );
  }
}

/// Represents a complete question with all its components
class Question {
  final Content question;
  final List<QuestionOption> options;
  final Content? explanation;
  final String? explanationImageUrl;

  Question({
    required this.question,
    required this.options,
    this.explanation,
    this.explanationImageUrl,
  });

  Map<String, dynamic> toJson() {
    return {
      'question': question.toJson(),
      'options': options.map((option) => option.toJson()).toList(),
      'explanation': explanation?.toJson(),
      'explanation_image_url': explanationImageUrl,
    };
  }

  factory Question.fromJson(Map<String, dynamic> json) {
    return Question(
      question: Content.fromJson(json['question'] as Map<String, dynamic>),
      options: (json['options'] as List)
          .map(
            (option) => QuestionOption.fromJson(option as Map<String, dynamic>),
          )
          .toList(),
      explanation: json['explanation'] != null
          ? Content.fromJson(json['explanation'] as Map<String, dynamic>)
          : null,
      explanationImageUrl: json['explanation_image_url'] as String?,
    );
  }
}

/// Represents the complete question bank
class QuestionBank {
  final List<Question> questions;

  QuestionBank({required this.questions});

  Map<String, dynamic> toJson() {
    return {
      'questions': questions.map((question) => question.toJson()).toList(),
    };
  }

  factory QuestionBank.fromJson(Map<String, dynamic> json) {
    return QuestionBank(
      questions: (json['questions'] as List)
          .map(
            (question) => Question.fromJson(question as Map<String, dynamic>),
          )
          .toList(),
    );
  }
}
