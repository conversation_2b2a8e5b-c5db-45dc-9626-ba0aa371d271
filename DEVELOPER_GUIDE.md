# دليل المطور - Developer Guide

## نظرة عامة على البنية

### نماذج البيانات (Data Models)

#### ContentType Enum
```dart
enum ContentType {
  text,    // نص عادي
  latex,   // معادلات LaTeX
  image,   // صور من Google Drive
}
```

#### Content Class
يمثل محتوى السؤال أو الخيار أو الشرح:
```dart
class Content {
  final String content;      // النص أو الرابط
  final ContentType type;    // نوع المحتوى
}
```

#### QuestionOption Class
يمثل خيار إجابة واحد:
```dart
class QuestionOption {
  final String answer;       // نص الخيار
  final ContentType type;    // نوع المحتوى
  final bool isCorrect;      // هل هو الإجابة الصحيحة
}
```

#### Question Class
يمثل السؤال الكامل:
```dart
class Question {
  final Content question;                    // نص السؤال
  final List<QuestionOption> options;        // خيارات الإجابة
  final Content? explanation;                // الشرح (اختياري)
  final String? explanationImageUrl;        // رابط صورة الشرح (اختياري)
}
```

### الخدمات (Services)

#### ExportService
مسؤول عن تصدير الأسئلة إلى JSON:

**الوظائف الرئيسية:**
- `exportQuestions()`: تصدير قائمة الأسئلة
- `validateExportedJson()`: التحقق من صحة JSON
- `createJsonPreview()`: إنشاء معاينة للـ JSON

### أدوات التحقق (Validators)

#### Validators Class
يحتوي على دوال التحقق من صحة البيانات:

```dart
class Validators {
  static bool isNotEmpty(String? value)
  static bool isValidGoogleDriveUrl(String url)
  static bool isValidLatex(String latex)
  static bool hasCorrectAnswer(List<bool> correctAnswers)
  static bool hasMinimumOptions(List<dynamic> options, {int minimum = 2})
}
```

### الواجهات (Widgets)

#### QuestionFormWidget
النموذج الرئيسي لإدخال السؤال:

**المكونات:**
- حقل السؤال مع اختيار النوع
- قائمة خيارات الإجابة (2-10 خيارات)
- حقل الشرح الاختياري
- حقل رابط صورة الشرح

#### QuestionsListWidget
عرض قائمة الأسئلة المضافة:

**المميزات:**
- عرض مختصر لكل سؤال
- إمكانية توسيع لرؤية التفاصيل
- زر حذف لكل سؤال
- تمييز الإجابات الصحيحة

## إرشادات التطوير

### إضافة ميزات جديدة

#### 1. إضافة نوع محتوى جديد
```dart
// في question_models.dart
enum ContentType {
  text,
  latex,
  image,
  video,  // نوع جديد
}

// تحديث الدوال المساعدة
String _getTypeLabel(ContentType type) {
  switch (type) {
    case ContentType.video:
      return 'فيديو';
    // باقي الحالات...
  }
}
```

#### 2. إضافة تحقق جديد
```dart
// في validators.dart
static bool isValidVideoUrl(String url) {
  // منطق التحقق من رابط الفيديو
  return url.contains('youtube.com') || url.contains('vimeo.com');
}
```

#### 3. إضافة حقل جديد للسؤال
```dart
// في question_models.dart
class Question {
  final Content question;
  final List<QuestionOption> options;
  final Content? explanation;
  final String? explanationImageUrl;
  final int? difficulty;  // حقل جديد للصعوبة
  
  // تحديث toJson() و fromJson()
}
```

### أفضل الممارسات

#### 1. إدارة الحالة
- استخدم `setState()` للتحديثات البسيطة
- فكر في استخدام Provider أو Bloc للحالات المعقدة

#### 2. التحقق من الصحة
- تحقق من البيانات على مستوى النموذج
- أضف رسائل خطأ واضحة باللغة العربية
- تحقق من الروابط قبل الحفظ

#### 3. تجربة المستخدم
- أضف مؤشرات التحميل للعمليات الطويلة
- استخدم SnackBar للرسائل السريعة
- أضف تأكيدات للعمليات المدمرة (حذف)

#### 4. الأداء
- استخدم `ListView.builder` للقوائم الطويلة
- تخلص من Controllers في dispose()
- تجنب إعادة البناء غير الضرورية

### اختبار التطبيق

#### اختبارات الوحدة
```dart
// اختبار نماذج البيانات
test('Question model serialization', () {
  final question = Question(/* ... */);
  final json = question.toJson();
  final restored = Question.fromJson(json);
  expect(restored.question.content, equals(question.question.content));
});

// اختبار أدوات التحقق
test('Google Drive URL validation', () {
  expect(Validators.isValidGoogleDriveUrl('https://drive.google.com/file/d/123'), isTrue);
  expect(Validators.isValidGoogleDriveUrl('https://example.com'), isFalse);
});
```

#### اختبارات الواجهة
```dart
testWidgets('Add question button works', (WidgetTester tester) async {
  await tester.pumpWidget(const QuestionCreatorApp());
  
  // ملء النموذج
  await tester.enterText(find.byType(TextFormField).first, 'سؤال تجريبي');
  
  // إضافة السؤال
  await tester.tap(find.text('إضافة السؤال'));
  await tester.pumpAndSettle();
  
  // التحقق من النتيجة
  expect(find.text('تم إضافة السؤال بنجاح!'), findsOneWidget);
});
```

### نصائح للصيانة

#### 1. تحديث التبعيات
```bash
# فحص التحديثات المتاحة
flutter pub outdated

# تحديث التبعيات
flutter pub upgrade

# تحديث Flutter نفسه
flutter upgrade
```

#### 2. تحسين الأداء
```bash
# تحليل حجم التطبيق
flutter build apk --analyze-size

# فحص الأداء
flutter run --profile
```

#### 3. إصلاح المشاكل الشائعة
- **مشكلة الذاكرة**: تأكد من dispose() للـ Controllers
- **مشكلة التخطيط**: استخدم SingleChildScrollView للمحتوى الطويل
- **مشكلة الحالة**: تأكد من استدعاء setState() في المكان الصحيح

### التوثيق

#### تعليقات الكود
```dart
/// Creates a new question with the given parameters.
/// 
/// [question] The main question content
/// [options] List of answer options (minimum 2, maximum 10)
/// [explanation] Optional explanation for the answer
/// 
/// Returns the created [Question] object
/// Throws [ArgumentError] if validation fails
Question createQuestion({
  required Content question,
  required List<QuestionOption> options,
  Content? explanation,
}) {
  // Implementation...
}
```

#### تحديث README
- أضف أمثلة للاستخدام الجديد
- وثق أي تغييرات في API
- أضف لقطات شاشة للميزات الجديدة

هذا الدليل يوفر أساساً قوياً لتطوير وصيانة التطبيق. لا تتردد في إضافة المزيد من التفاصيل حسب احتياجات فريق التطوير.
